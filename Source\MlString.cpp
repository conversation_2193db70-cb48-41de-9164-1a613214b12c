#include "MlString.h"

namespace MultiLang
{
  LCID  LocaleId ;
  const wchar_t*  SupportedCultures[] = { L"en-US", L"de", L"en-GB" } ; //MLHIDE
  const int       NumberOfCultures = sizeof SupportedCultures / sizeof SupportedCultures[0] ;

  const CString ml_string ( int StringId )
  {
    CString   s ;

    // Try to load a resource for the specific locale.
    BOOL      ResourceOK = s.LoadString ( NULL, StringId, (WORD)LocaleId ) ;
    
    // If it fails, then load for the default locale.
    if ( !ResourceOK )
    {
      s.LoadString ( StringId ) ;
    }

    return (const TCHAR *)s ;
  }
}
